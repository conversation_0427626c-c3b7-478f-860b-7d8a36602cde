import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import './index.css';
import { cleanupService } from './services/cleanupService';

// Start cleanup service for expired QR tokens
// cleanupService.start(); // Temporarily disabled until Firestore index is created

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>
);
