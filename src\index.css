@tailwind base;
@tailwind components;
@tailwind utilities;

/* Mobile-first optimizations */
@layer base {
  html {
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
  }

  body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    touch-action: manipulation;
  }

  /* Prevent zoom on input focus */
  input, textarea, select {
    font-size: 16px;
  }

  /* Better touch targets */
  button, a, [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }
}

@layer utilities {
  .touch-manipulation {
    touch-action: manipulation;
  }

  .active\:scale-95:active {
    transform: scale(0.95);
  }

  .active\:scale-98:active {
    transform: scale(0.98);
  }
}

/* QR Scanner Styles */
#qr-scanner-container {
  border-radius: 12px;
  overflow: hidden;
}

#qr-scanner-container video {
  border-radius: 12px;
  width: 100% !important;
  height: auto !important;
}

#qr-scanner-container canvas {
  border-radius: 12px;
}

/* Hide default QR scanner UI elements */
#qr-scanner-container .qr-scanner-stop-button {
  display: none !important;
}

#qr-scanner-container .qr-scanner-torch-button {
  background: rgba(139, 92, 246, 0.8) !important;
  border: none !important;
  border-radius: 8px !important;
  color: white !important;
  padding: 8px 16px !important;
  margin: 8px !important;
}

#qr-scanner-container .qr-scanner-zoom-slider {
  margin: 8px !important;
}

/* Custom scanner overlay */
#qr-scanner-container .qr-scanner-region {
  border: 2px solid rgba(139, 92, 246, 0.8) !important;
  border-radius: 12px !important;
}
