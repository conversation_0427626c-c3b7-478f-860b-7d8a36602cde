rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users collection - allow read/write for all authenticated users
    match /users/{userId} {
      allow read, write: if true;
    }
    
    // QR Tokens collection - allow read/write for all authenticated users
    match /qr-tokens/{tokenId} {
      allow read, write: if true;
    }
    
    // Pending Scores collection - allow read/write for all authenticated users
    match /pending-scores/{pendingId} {
      allow read, write: if true;
    }
    
    // Booth QRs collection - allow read/write for all authenticated users
    match /booth-qrs/{boothId} {
      allow read, write: if true;
    }
    
    // Allow all other documents for development
    match /{document=**} {
      allow read, write: if true;
    }
  }
}
